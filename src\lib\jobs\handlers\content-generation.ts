import { Job, <PERSON>Handler, ContentGenerationJobData } from '../types';
import { AIContentGenerator, GenerationOptions } from '../../ai';

export class ContentGenerationHandler implements JobHandler {
  private aiGenerator: AIContentGenerator;

  constructor() {
    this.aiGenerator = new AIContentGenerator();
  }

  async handle(job: Job): Promise<any> {
    const data = job.data as ContentGenerationJobData;

    if (!process.env.CONTENT_GENERATION_ENABLED || process.env.CONTENT_GENERATION_ENABLED !== 'true') {
      throw new Error('Content generation is disabled');
    }

    try {
      // Prepare content for AI generation
      const scrapedContent = this.prepareScrapedContent(data);

      // Set generation options based on job data
      const options: GenerationOptions = {
        complexity: data.complexity || 'medium',
        priority: data.priority || 'quality',
        contentQuality: data.contentQuality || 70,
        scrapingCost: data.scrapingCost || 0,
        maxRetries: 3
      };

      console.log(`Starting AI content generation for: ${data.url}`);
      console.log(`Content size: ${scrapedContent.length} characters`);
      console.log(`Options:`, options);

      // Generate content using the enhanced AI system
      const result = await this.aiGenerator.generateContent(
        scrapedContent,
        data.url,
        options
      );

      if (!result.success) {
        throw new Error(`AI content generation failed: ${result.error}`);
      }

      console.log(`Content generation successful using ${result.modelUsed?.provider}/${result.modelUsed?.model}`);
      console.log(`Validation score: ${result.validation?.score || 'N/A'}`);

      return {
        success: true,
        content: result.content,
        generatedAt: result.timestamp,
        modelUsed: result.modelUsed,
        tokenUsage: result.tokenUsage,
        validation: result.validation,
        strategyUsed: result.strategyUsed
      };
    } catch (error) {
      console.error('Content generation failed:', error);
      throw error;
    }
  }

  /**
   * Prepare scraped content for AI processing
   */
  private prepareScrapedContent(data: ContentGenerationJobData): string {
    const { url, scrapedData, pricingData, faqData } = data;

    if (!scrapedData) {
      throw new Error('No scraped data available for content generation');
    }

    // Build comprehensive content string for AI processing
    let content = `# AI Tool Analysis\n\n`;
    content += `**URL:** ${url}\n\n`;

    if (scrapedData.title) {
      content += `**Title:** ${scrapedData.title}\n\n`;
    }

    if (scrapedData.description) {
      content += `**Description:** ${scrapedData.description}\n\n`;
    }

    if (scrapedData.textContent) {
      content += `**Main Content:**\n${scrapedData.textContent}\n\n`;
    }

    if (scrapedData.pricingText) {
      content += `**Pricing Information:**\n${scrapedData.pricingText}\n\n`;
    }

    if (scrapedData.faqText) {
      content += `**FAQ Content:**\n${scrapedData.faqText}\n\n`;
    }

    // Add structured pricing data if available
    if (pricingData) {
      content += `**Structured Pricing Data:**\n${JSON.stringify(pricingData, null, 2)}\n\n`;
    }

    // Add structured FAQ data if available
    if (faqData) {
      content += `**Structured FAQ Data:**\n${JSON.stringify(faqData, null, 2)}\n\n`;
    }

    // Add any additional scraped data
    if (scrapedData.features && scrapedData.features.length > 0) {
      content += `**Extracted Features:**\n${scrapedData.features.join('\n')}\n\n`;
    }

    if (scrapedData.socialLinks) {
      content += `**Social Links:**\n`;
      Object.entries(scrapedData.socialLinks).forEach(([platform, url]) => {
        if (url) content += `- ${platform}: ${url}\n`;
      });
      content += '\n';
    }

    return content;
  }

}
